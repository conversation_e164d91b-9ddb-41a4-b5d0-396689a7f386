<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="比特熊极简门户网站 - 专注于技术学习、游戏娱乐和社区交流的综合平台">
    <meta name="keywords" content="比特熊,技术学习,在线游戏,社区交流,资源共享">
    <meta name="author" content="比特熊团队">
    <meta name="robots" content="index, follow">
    <meta property="og:title" content="比特熊极简门户网站">
    <meta property="og:description" content="一起度过美好时光，探索技术，享受游戏，分享知识">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://bitbear.example.com">
    <meta property="og:image" content="image/bit.png">
    <title>比特熊极简门户网站 - 一起度过美好时光</title>
    <!-- 设置页面标题 -->
    <link rel="stylesheet" href="index-style.css?v=20250731v1">
    <!-- 引入主要样式表 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <!-- 预连接到Google字体服务器，提高加载速度 -->
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- 引入现代化的Inter字体 -->
    <link rel="icon" type="image/png" href="image/bit.png">
    <!-- 设置网站图标 -->
    
    <style>
        /* 独立版本的额外样式 */
        .standalone-notice {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
            color: #92400e;
            padding: 12px 20px;
            text-align: center;
            font-size: 14px;
            font-weight: 500;
            border-bottom: 1px solid #f59e0b;
            position: relative;
            z-index: 1000;
        }
        
        .standalone-notice::before {
            content: '⚠️';
            margin-right: 8px;
        }
        
        /* 确保统计数据在独立模式下正常显示 */
        .stat-number[data-stat="currentUsers"]::after { content: '12,847'; }
        .stat-number[data-stat="projectCount"]::after { content: '2,500+'; }
        .stat-number[data-stat="postCount"]::after { content: '45,892'; }
        .stat-number[data-stat="onlineHours"]::after { content: '2,847,392'; }
        
        .stat-number {
            position: relative;
        }
        
        .stat-number::after {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: inherit;
            font-weight: inherit;
            background: inherit;
            -webkit-background-clip: inherit;
            -webkit-text-fill-color: inherit;
            background-clip: inherit;
        }
        
        /* 隐藏原始文本 */
        .standalone-mode .stat-number {
            color: transparent;
        }
    </style>
</head>
<body class="standalone-mode">
    <!-- 独立版本提示 -->
    <div class="standalone-notice">
        独立版本 - 此版本可直接在浏览器中打开，无需服务器。某些动态功能可能受限。
    </div>
    
    <!-- 网站导航条部分 -->
    <nav class="navbar">
        <div class="center">
        <!-- 网站区域：居中盒子 让内容居中显示-->
            <img src="image/bit.png" width="130px" height="60px">
            <!-- 插入比特熊logo图片-->
            <ul class="list-left">
            <!-- 导航条部分 左边区域-->
                <li class="nav-item dropdown">
                <!-- 第一项：组织下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        组织
                        <!-- 下拉箭头图标 -->
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <polyline points="6 9 12 15 18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">组织介绍</a>
                        <a href="#" class="dropdown-item">组织架构</a>
                        <a href="#" class="dropdown-item">组织领导</a>
                        <a href="#" class="dropdown-item">加入我们</a>
                    </div>
                </li>
                <li class="nav-item dropdown">
                <!-- 第二项：服务下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        服务
                        <!-- 下拉箭头图标 -->
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <polyline points="6 9 12 15 18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">在线咨询</a>
                        <a href="#" class="dropdown-item">技术支持</a>
                        <a href="#" class="dropdown-item">喊话我们</a>
                        <a href="#" class="dropdown-item">资源共享</a>
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <!--  第三项：游戏下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        一起玩游戏
                        <!-- 下拉箭头图标 -->
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <polyline points="6 9 12 15 18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">在线游戏</a>
                        <a href="#" class="dropdown-item">游戏排行榜</a>
                        <a href="#" class="dropdown-item">游戏攻略</a>
                        <a href="#" class="dropdown-item">游戏社区</a>
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <!-- 第四项：社区下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        社区|资讯局
                        <!-- 下拉箭头图标 -->
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <polyline points="6 9 12 15 18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">最新资讯</a>
                        <a href="#" class="dropdown-item">技术文章</a>
                        <a href="#" class="dropdown-item">讨论论坛</a>
                        <a href="#" class="dropdown-item">活动公告</a>
                    </div>
                </li>
                <li class="nav-item dropdown">
                    <!--  第五项：资源共享下拉菜单-->
                    <a href="#" class="nav-link dropdown-toggle">
                        资源共享
                        <!-- 下拉箭头图标 -->
                        <svg width="12" height="12" viewBox="0 0 24 24" fill="none">
                            <polyline points="6 9 12 15 18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                    <!-- 下拉菜单内容 -->
                    <div class="dropdown-menu">
                        <a href="#" class="dropdown-item">学习资料</a>
                        <a href="#" class="dropdown-item">开发工具</a>
                        <a href="#" class="dropdown-item">模板下载</a>
                        <a href="#" class="dropdown-item">代码库</a>
                    </div>
                </li>
                <li class="nav-item">
                    <!--第六项：项目规划和建设（无下拉菜单）-->
                    <a href="#" class="nav-link">项目规划和建设</a>
                </li>
            </ul>
            
            <!-- 搜索框区域 -->
            <div class="search-container">
                <form class="search-form" role="search">
                    <div class="search-input-wrapper">
                        <input type="search" 
                               class="search-input" 
                               placeholder="搜索课程、文章、资源..." 
                               aria-label="搜索内容"
                               autocomplete="off">
                        <button type="submit" class="search-btn" aria-label="搜索">
                            <!-- 搜索图标 -->
                            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                                <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                                <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </button>
                    </div>
                    
                    <!-- 搜索建议下拉框 -->
                    <div class="search-suggestions" id="searchSuggestions">
                        <!-- 热门搜索标签 -->
                        <div class="suggestions-section">
                            <h4 class="suggestions-title">热门搜索</h4>
                            <div class="suggestion-tags">
                                <span class="suggestion-tag">JavaScript</span>
                                <span class="suggestion-tag">Python</span>
                                <span class="suggestion-tag">React</span>
                                <span class="suggestion-tag">Vue.js</span>
                                <span class="suggestion-tag">Node.js</span>
                            </div>
                        </div>
                        
                        <!-- 最近搜索 -->
                        <div class="suggestions-section">
                            <h4 class="suggestions-title">最近搜索</h4>
                            <ul class="recent-searches">
                                <li><a href="#" class="recent-search-item">在线课程</a></li>
                                <li><a href="#" class="recent-search-item">课程超市</a></li>
                                <li><a href="#" class="recent-search-item">数据分析</a></li>
                            </ul>
                        </div>
                    </div>
                </form>
            </div>

            <ul class="list-right">
            <!-- 导航条部分 右边区域-->
                <!-- 移动端搜索按钮 -->
                <li class="mobile-search-btn-container">
                    <button class="mobile-search-btn" aria-label="搜索">
                        <!-- 搜索图标 -->
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                            <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </button>
                </li>
                <li class="zhuche">
                <!--    注册区域-->
                    <a href="#register">注册试试</a>
                <!--  创建注册链接-->
                </li>
                <li class="loading">
                <!-- 登录区域-->
                    <a href="#login">登录</a>
                <!--  创建登录链接-->
                </li>
                <li class="admin-link">
                <!--    管理后台链接-->
                    <a href="#admin" title="管理后台 (需要登录)">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </a>
                </li>
            </ul>
        </div>
    </nav>
